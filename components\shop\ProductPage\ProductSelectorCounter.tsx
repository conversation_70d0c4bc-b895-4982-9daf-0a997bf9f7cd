"use client"
import CustomButton from "@/components/UI/CustomButton"
import { Plus, Minus, ShoppingBasket, CheckCircle2, X } from "lucide-react"
import { useState, useRef, useMemo } from "react"
import { Variation } from "@/lib/types/product.types"
import { useCart } from "@/lib/context/cart-context"
import CartConfirmationModal from "./CartConfirmationModal"
import MobileBottomSheet from "@/components/UI/MobileBottomSheet"
import Link from "next/link"
import Image from "next/image"

interface ProductSelectorCounterProps {
    selectedVariant?: Variation | null;
    productInfo?: {
        image?: string;
        name?: string;
        color?: string;
    };
}

const ProductSelectorCounter = ({ selectedVariant, productInfo }: ProductSelectorCounterProps) => {
    // const [isSticky, setIsSticky] = useState(false)
    const [isOpen, setIsOpen] = useState(false)

    const [showModal, setShowModal] = useState(false)
    const [updatingButton, setUpdatingButton] = useState<'add' | 'decrease' | null>(null);
    const sectionRef = useRef<HTMLDivElement>(null)
    console.warn(productInfo);


    const { cartItems, totalItems, isUpdating, addToCart: addItemToCart, decreaseFromCart } = useCart()
    // Calculate the quantity of the currently selected variant in the cart
    // Using useMemo to recalculate when cartItems or selectedVariant changes
    const currentVariantQuantity = useMemo(() => {

        return selectedVariant
            ? cartItems.find(item => item.id === selectedVariant.id)?.quantity || 0
            : 0;
    }, [cartItems, selectedVariant]);



    // useEffect(() => {
    //     const handleScroll = () => {
    //         if (sectionRef.current) {
    //             const sectionRect = sectionRef.current.getBoundingClientRect()
    //             // Show sticky when section is scrolled past (top of section is above viewport top)
    //             setIsSticky(sectionRect.top < 0)
    //         }
    //     }


    //     window.addEventListener('scroll', handleScroll)
    //     return () => window.removeEventListener('scroll', handleScroll)
    // }, [])


    const addToCart = async () => {
        if (selectedVariant && currentVariantQuantity < selectedVariant.current_quantity) {
            setUpdatingButton('add');
            await addItemToCart(selectedVariant, 1, productInfo);
            setUpdatingButton(null);
            if (currentVariantQuantity === 0) {
                setShowModal(true);
            }
        }
    };
    const stickyAddToCart = async () => {
        if (selectedVariant && currentVariantQuantity < selectedVariant.current_quantity) {
            setUpdatingButton('add');
            await addItemToCart(selectedVariant, 1, productInfo);
            setUpdatingButton(null);
            if (currentVariantQuantity === 0) {
                setIsOpen(true)
            }
        }
    }

    // Decrease quantity or remove the selected variant from cart
    const handleDecreaseFromCart = async () => {
        if (selectedVariant) {
            setUpdatingButton('decrease');
            await decreaseFromCart(selectedVariant);
            setUpdatingButton(null);
        }
    };

    return (
        <div className="">
            {/* <div className="mb-3 flex gap-3">
                <span>
                    قیمت نهایی:
                </span>
                <span className="text-primary font-bold">
                    {finalPrice.toLocaleString()} تومان
                </span>
            </div> */}
            {/* Original non-sticky version */}
            <div ref={sectionRef} >
                {
                    currentVariantQuantity > 0 &&
                    <div className={`flex items-center border border-gray-300 max-md:hidden rounded-2xl px-5 py-3 w-full ${isUpdating ? 'opacity-70' : ''}`}>
                        <button
                            className="bg-[#2DC058] disabled:bg-[#C5CBD4]/50 rounded-full flex items-center justify-center w-10 h-8"
                            onClick={addToCart}
                            disabled={isUpdating === 'decrease' || !selectedVariant || currentVariantQuantity >= selectedVariant?.current_quantity}
                        >
                            {updatingButton === 'add' ? (
                                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                            ) : (
                                <Plus size={16} color="white" />
                            )}
                        </button>
                        <input
                            type="tel"
                            dir="rtl"
                            className="left-direction text-center w-full bg-transparent text-lg font-semibold focus:outline-none"
                            value={currentVariantQuantity.toLocaleString()}
                            maxLength={8}
                            readOnly
                        />
                        <button
                            className="bg-[#C5CBD4] disabled:bg-[#C5CBD4]/50 rounded-full flex items-center justify-center w-10 h-8"
                            onClick={handleDecreaseFromCart}
                            disabled={currentVariantQuantity === 0 || isUpdating === 'add'}
                        >
                            {updatingButton === 'decrease' ? (
                                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                            ) : (
                                <Minus size={16} color="white" />
                            )}
                        </button>
                    </div>
                }
                {currentVariantQuantity === 0 && !!selectedVariant && !!selectedVariant.current_quantity && selectedVariant.current_quantity > 0 && (
                    <CustomButton
                        className='py-5 max-md:hidden flex items-center mt-5 w-full justify-center gap-2'
                        onClick={addToCart}
                        disabled={isUpdating === "add" || !selectedVariant}
                        loading={isUpdating === "add" || isUpdating === "decrease"}
                    >
                        <ShoppingBasket />
                        {/* show the button only when something is in the cart */}

                        <span>افزودن به سبد خرید</span>

                        {/* نمایش تعداد محصولات در سبد خرید */}
                        {totalItems > 0 && !isUpdating && (
                            <span className="bg-white text-green-600 rounded-full w-6 h-6 flex items-center justify-center text-sm">
                                {totalItems}
                            </span>
                        )}
                    </CustomButton>
                )}
            </div>

            {/* Sticky version - only shown in mobile devices */}
            <div className="fixed bottom-0 left-0 right-0 bg-white shadow-lg border-t h-20 border-gray-200 p-4 z-50 md:hidden">
                <div className="container mx-auto flex items-center justify-between gap-4 ">



                    {currentVariantQuantity === 0 && !!selectedVariant && !!selectedVariant.current_quantity && selectedVariant.current_quantity > 0 ? (
                        <CustomButton
                            className='py-3 flex items-center justify-center gap-2 flex-1 max-w-[50%]'
                            onClick={stickyAddToCart}
                            disabled={isUpdating === "add" || !selectedVariant}
                            loading={isUpdating === "add" || isUpdating === "decrease"}
                        >
                            <ShoppingBasket size={18} />
                            {/* Show "سبد خرید" only when current variant is not in cart */}
                            <span className="text-sm">سبد خرید</span>
                            {/* نمایش تعداد محصولات در سبد خرید */}
                            {totalItems > 0 && !isUpdating && (
                                <span className="bg-white text-green-600 rounded-full w-5 h-5 flex items-center justify-center text-xs">
                                    {totalItems}
                                </span>
                            )}
                        </CustomButton>
                    )
                        :
                        <div className={`flex items-center border border-gray-300 rounded-2xl px-3 py-2 flex-1 max-w-[200px] ${isUpdating ? 'opacity-70' : ''}`}>
                            <button
                                className="bg-[#2DC058] disabled:bg-[#C5CBD4]/50 rounded-full flex items-center justify-center w-8 h-6"
                                onClick={addToCart}
                                disabled={isUpdating === "decrease" || updatingButton === 'decrease' || !selectedVariant || currentVariantQuantity >= selectedVariant?.current_quantity}
                            >
                                {updatingButton === 'add' ? (
                                    <div className="w-3 h-3 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                                ) : (
                                    <Plus size={14} color="white" />
                                )}
                            </button>
                            <input
                                type="tel"
                                dir="rtl"
                                className="left-direction text-center w-full bg-transparent text-base font-semibold focus:outline-none"
                                value={currentVariantQuantity.toLocaleString()}
                                maxLength={8}
                                readOnly
                            />
                            <button
                                className="bg-[#C5CBD4] disabled:bg-[#C5CBD4]/50 rounded-full flex items-center justify-center w-8 h-6"
                                onClick={handleDecreaseFromCart}
                                disabled={currentVariantQuantity === 0 || isUpdating === "add" || updatingButton === 'add'}
                            >
                                {updatingButton === 'decrease' ? (
                                    <div className="w-3 h-3 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                                ) : (
                                    <Minus size={14} color="white" />
                                )}
                            </button>
                        </div>
                    }
                    {
                        selectedVariant?.sale_price ?
                            <div className="flex flex-col absolute left-4 -top-2 gap-2 h-full">
                                <div className="relative inline-block text-gray-400 text-sm font-bold rtl mt-5">
                                    <span className="text-red-400">
                                        {selectedVariant ?
                                            `${selectedVariant.price.toLocaleString()} تومان` :
                                            'محصول موجود نیست'
                                        }
                                    </span>

                                    {/* Diagonal Line */}
                                    {selectedVariant?.sale_price && (
                                        <div className="absolute left-0 top-1/2 w-full border-t border-gray-500 rotate-[-10deg]"></div>
                                    )}
                                    {/* <div className="absolute left-0 top-1/2 w-full border-t border-gray-500 rotate-[-10deg]"></div> */}
                                </div>
                                <span className="text-xl pl-5 absolute left-0 bottom-0"> {selectedVariant?.sale_price?.toLocaleString()} </span>
                            </div>
                            :
                            <div className="px-5 text-lg">
                                <span>
                                    {selectedVariant?.price && `${selectedVariant?.price?.toLocaleString()} تومان`}
                                </span>
                            </div>
                    }
                </div>
            </div>
            <MobileBottomSheet isOpen={isOpen} onClose={() => setIsOpen(false)}>
                <div className="max-md:w-full p-0 gap-0 rounded-2xl">
                    <div className="relative flex flex-col ">
                       

                        <div className='mb-5 pb-5'>
                            {/* Single Close Button at top-left (RTL) */}
                            <button
                                onClick={() => setIsOpen(false)}
                                className="absolute left-4 top-4 rounded-full p-1.5 border-2 border-gray-300 hover:bg-gray-100 transition-colors">
                                <X className="h-5 w-5 text-gray-500" />
                            </button>

                            <div className="flex items-center gap-2 text-primary absolute right-4 top-4">
                                <div className="bg-primary/10 rounded-full p-1">
                                    <CheckCircle2 className="h-6 w-6" />
                                </div>
                                <span className="font-semibold">محصول به سبد خرید اضافه شد</span>
                            </div>
                        </div>

                        <div className="flex items-center gap-4 border-t-2 mt-5 pt-5 pb-3 px-5">
                            {productInfo?.image && (
                                <div className="relative w-24 h-24 border border-gray-100 rounded-2xl overflow-hidden bg-gray-50">
                                    <Image
                                        fill
                                        src={productInfo.image}
                                        alt={productInfo.name || ''}
                                        className="object-cover"
                                    />
                                </div>
                            )}
                            <div className="flex-1">
                                <h3 className="font-semibold mb-2 text-gray-900">{productInfo?.name}</h3>
                              
                                {
                                    selectedVariant?.attributes?.map(detail => (
                                        <span key={detail.title} className="text-sm ml-3 text-gray-500"><span>{detail.title}</span>: {detail.value}</span>
                                    ))

                                }
                            </div>
                        </div>
                    </div>

                    <div className="flex border-t border-gray-100 overflow-hidden px-6 mx-auto mb-3">
                        
                        <Link
                            href="/checkout/cart"
                            className="flex-1 rounded-xl rounded-b-md p-4 overflow-hidden text-center bg-primary text-white hover:bg-primary/90 transition-colors font-medium"
                        >
                            مشاهده سبد خرید
                        </Link>
                    </div>
                </div>
            </MobileBottomSheet>
            {/* {isSticky && (
            )} */}

            <CartConfirmationModal
                open={showModal}
                onClose={() => setShowModal(false)}
                productInfo={{
                    image: productInfo?.image,
                    name: productInfo?.name,
                    color: selectedVariant?.color,
                    details: selectedVariant?.attributes
                }}
            />
        </div>
    )
}

export default ProductSelectorCounter
