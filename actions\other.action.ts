"use server"


import { apiClient } from "@/lib/apiClient";
import CookieService from "@/lib/services/cookie-service";
import { getUserAddresses } from "@/lib/utils";
// import { cookies } from "next/headers";

export const getServicesAction = async () => {
    const response = await (await apiClient(`services`, {
        next: { revalidate: 900 }
    })).json()
    return response

}

export async function SetNaghlieCookie(token: string, expire?: number) {
    try {
        console.log('SetNaghlieCookie: Starting with token:', token);
        console.log('SetNaghlieCookie: Expire time:', expire);

        await CookieService.setAuthorizationToken(token, expire)

        console.log('SetNaghlieCookie: Cookie set successfully');
        return { success: true };

    } catch (error) {
        console.log('SetNaghlieCookie: Error setting authorization cookie:', error);
        throw error; // Re-throw to handle in the component
    }
}

/**
 * Send notification token to the server
 * @param token Firebase notification token
 * @param path Current path when the token was generated
 * @param status Accept or reject status
 * @param userAgent User's browser and device information
 * @param ip User's IP address (optional, will be determined server-side if not provided)
 * @returns Response from the API
 */
export async function sendNotificationToken(
    token: string,
    path: string,
    status: string,
    userAgent: string = '',
    ip: string = ''
) {
    try {

        const response = await apiClient("notification/client-token", {
            method: "POST",
            body: {
                token,
                path,
                app: "khodrox",
                status,
                agent: userAgent,
                ip
            }
        });

        const data = await response.json();
        return { success: response.ok, data };
    } catch (error) {
        console.error("Error sending notification token:", error);
        return { success: false, error: "Failed to send notification token" };
    }
}

