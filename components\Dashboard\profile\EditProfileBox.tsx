'use client'
import CustomButton from '@/components/UI/CustomButton'
import JalaliDatePicker from '@/components/UI/JalaliDatePicker'
import { Camera } from 'lucide-react'
import React, { useState } from 'react'
import { Value } from 'react-multi-date-picker'
const EditProfileBox = () => {
    const [birthDate, setBirthDate] = useState<Value | null>("")
  return (
    <div className="flex flex-col gap-4 bg-white p-6 rounded-2xl shadow-md min-h-96">
            <div className="md:grid grid-cols-2 gap-x-10 h-full md:w-[90%] max-md:flex max-md:flex-col max-md:gap-4">
                <div className="flex flex-col max-md:gap-4 justify-between">
                    <div className="flex flex-col gap-3">
                        <label htmlFor="fullName">نام و نام خانوادگی</label>
                        <input
                            type="text"
                            id="fullName"
                            className="w-full p-3 border border-gray-300 rounded-xl focus:border-primary outline-none"
                        />
                    </div>

                    <div className="flex flex-col gap-3">
                        <label htmlFor="phone">شماره تلفن</label>
                        <input
                            type="text"
                            id="phone"
                            className="w-full p-3 border border-gray-300 rounded-xl focus:border-primary outline-none"
                        />
                    </div>

                    <div className="flex flex-col gap-3">
                        <label htmlFor="email">ایمیل</label>
                        <input
                            type="email"
                            id="email"
                            className="w-full p-3 border border-gray-300 rounded-xl focus:border-primary outline-none"
                        />
                    </div>
                </div>

                {/* Modified profile picture section */}
                <div className="group relative">
                    <input
                        type="file"
                        id="profilePhoto"
                        accept=".jpg,.jpeg,.png"
                        className="hidden"
                        onChange={(e) => {                            
                            if (e.target.files && e.target.files[0]) {
                                const file = e.target.files[0];
                                // the other code to handle the file upload will be added here
                            }
                        }}
                    />
                    <label
                        htmlFor="profilePhoto"
                        className="flex flex-col justify-center items-center border-2 border-dashed border-gray-300 rounded-3xl p-6 cursor-pointer group-hover:border-primary transition-colors"
                    >
                        <div className="p-2 border-dashed border-2 border-gray-300 rounded-full mb-4 group-hover:border-primary transition-colors">
                            <div className="w-28 h-28 bg-gray-400 flex items-center justify-center rounded-full">
                                <Camera stroke="white" size={30} />
                            </div>
                        </div>
                        <h3 className="text-center mb-2">عکس پروفایل</h3>
                        <p className="text-sm text-gray-500 mb-1">
                            فرمت مورد قبول: **.jpg, *.png, *.jpeg**
                        </p>
                        <p className="text-sm">حداکثر حجم فایل: 3 مگابایت</p>
                    </label>
                </div>
            </div>

            <div className="md:grid grid-cols-2 gap-x-10 h-full md:w-[90%] mt-5 max-md:flex max-md:flex-col max-md:gap-4">
                <div className="flex flex-col gap-3">
                    <label htmlFor="birthDate">تاریخ تولد</label>
                    <JalaliDatePicker value={birthDate} setValue={setBirthDate} />
                </div>

                <div className="flex flex-col gap-3">
                    <label htmlFor="nationalCode">کد ملی</label>
                    <input
                        placeholder='کد ملی'
                        type="text"
                        id="nationalCode"
                        className="w-full p-3 border border-gray-300 rounded-xl focus:border-primary outline-none"
                    />
                </div>
            </div>
            <div>
                <CustomButton className='w-fit px-8 py-4 mt-5'>
                    ویرایش و ذخیره
                </CustomButton>
            </div>
        </div>
  )
}

export default EditProfileBox