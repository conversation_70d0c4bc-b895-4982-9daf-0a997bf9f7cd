// import LastOrders from '@/components/Dashboard/LastOrders'
import PopularServices from '@/components/Dashboard/PopularServices'
import UserLastInquiries from '@/components/Dashboard/UserLastInquiries'
import UserQuickAccess from '@/components/Dashboard/UserQuickAccess'
import WalletAmount from '@/components/Dashboard/WalletAmount'
import DashboardNavbar from '@/components/Header/DashboardNavbar'
import Sidebar from '@/components/Header/Sidebar'
import React from 'react'
import "@/styles/styles.css"

import { getInquiryHistory } from '@/actions/inquiry.action'
import LastOrders from '@/components/Dashboard/LastOrders'



const page = async () => {

    const response = await getInquiryHistory()

    const inquiries = response?.data ?? []
    console.log(typeof inquiries.length);
    

    
    return (
                <div className=' flex flex-col gap-4 h-full'>
                    <UserQuickAccess inquirieslength={inquiries.length} />
                    <div className=' grid lg:grid-cols-4 md:grid-cols-1 max-md:grid-cols-1 md:gap-y-4 lg:gap-4 max-md:gap-4'>
                        <UserLastInquiries title='آخرین استعلامات من' inquiries={inquiries} error={response.message} />
                        <div className='w-full flex flex-col gap-4 max-md:px-3 h-full '>
                            <WalletAmount />
                            <PopularServices />
                        </div>
                    </div>
                        <LastOrders />
                </div>
        
    )
}

export default page