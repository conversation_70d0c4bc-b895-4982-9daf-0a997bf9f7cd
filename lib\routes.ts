export const BASE_PATH = 'https://khodrox.com'
export const LOGIN_PATH = '/login'
export const PAYMENT_PATH = '/wallet'
export const INQUIRY_RESULT_PATH = '/tickets-result'
export const PAYMENT_RESULT_PATH = '/wallet/result'
export const CAR_TICKETS_PATH = '/car-tickets'
export const CAR_TICKETS_WITHDETAILS_PATH = CAR_TICKETS_PATH + '/car-tickets-withdetails'
export const CAR_TICKETS_WITHOUTDETAILS_PATH = CAR_TICKETS_PATH + '/car-tickets-withoutdetails'
export const CAR_VIOLATION_IMAGE_PATH = CAR_TICKETS_PATH + '/car-violation-image'
export const MOTOR_TICKETS_PATH = '/motor-tickets'
export const MOTOR_INSURANCE = '/motor-insurance'
export const MOTOR_TICKETS_WITHDETAILS_PATH = MOTOR_TICKETS_PATH + '/motor-tickets-withdetails'
export const MOTOR_TICKETS_WITHOUTDETAILS_PATH = MOTOR_TICKETS_PATH + '/motor-tickets-withoutdetails'
export const MOTOR_VIOLATION_IMAGE_PATH = MOTOR_TICKETS_PATH + '/motor-violation-image'
export const CAR_TAX_PATH = '/car-tax'
export const PLATE_HISTORY_PATH = '/plate-history'
export const CAR_PLATE_CHECK_PATH = '/car-plate-check'
export const CAR_ID_DOCUMENTS_PATH = '/carid-documents'
export const CAR_INSURANCE_PATH = '/car-insurance'
export const CAR_CHECKUP_PATH = '/car-checkup'
export const DRIVING_LICENSE_POINT_PATH = '/driving-license-point'
export const DRIVING_LICENSE_STATUS_PATH = '/driving-license-status'
export const HOME_PATH = '/'
export const BLOG_PATH = '/blog'
export const CONTACT_US_PATH = '/contact-us'
export const DASHBOARD_PATH = '/dashboard'
export const DASHBOARD_INQUIRY_HISTORY_PATH = '/dashboard/inquiry-history'
export const DASHBOARD_USER_ADDRESSES_PATH = '/dashboard/user-addresses'
export const DASHBOARD_PROFILE_PATH = '/dashboard/profile'
export const RULES_PATH = '/rules'
export const PRIVACY_POLICY_PATH = '/privacy-policy'
export const ERROR_PATH = '/error'
export const TICKET_RESULT_PATH = '/tickets-result'

// Checkout routes
export const CHECKOUT_CART_PATH = '/checkout/cart'
export const CHECKOUT_SHIPPING_PATH = '/checkout/shipping'
export const CHECKOUT_PAYMENT_PATH = '/checkout/payment'

// Protected routes that require authentication
// Users will be redirected to login page with return URL if not authenticated
export const protectedRoutes: string[] = [
    PAYMENT_PATH,
    DASHBOARD_PATH,
    CHECKOUT_SHIPPING_PATH,  // Requires auth for address selection
    CHECKOUT_PAYMENT_PATH    // Requires auth for payment processing
]
export const signOutOnlyRoutes = [LOGIN_PATH]

