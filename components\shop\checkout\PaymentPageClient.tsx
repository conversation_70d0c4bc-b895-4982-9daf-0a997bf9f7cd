'use client';

import { useState } from 'react';
import PaymentMethodCard from './PaymentMethodCard';
import FactorCard from './FactorCard';
import OrderSummary from './OrderSummary';
import { CartApiItem } from '@/lib/context/cart-context';
import { createInvoice } from '@/actions/payment.action';
import { CreateInvoiceResponse } from '@/lib/types/invoice.types';
import toast from 'react-hot-toast';
import { invoicePayment } from '../../../actions/payment.action';
import { useRouter } from 'nextjs-toploader/app';

type PaymentPageClientProps = {
  orderSummaryData?: {
    items?: CartApiItem[];
    total?: number;
    total_discount?: number;
    user_id?: string;
    subtotal?: number;
  };
  addressId?: string;
};

const PaymentPageClient = ({ orderSummaryData, addressId }: PaymentPageClientProps) => {
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<string>('wallet');
  const router = useRouter()
  
  const handleCreateInvoice = async () => {
    if (addressId) {      
      const response: CreateInvoiceResponse = await createInvoice(addressId);
      console.log(response);
      if (response.success) {
        if ('id' in response.data) {
          const payResponse = await invoicePayment(response.data.id, selectedPaymentMethod);
          console.log(payResponse);
          router.push(payResponse.data?.payment_link)
        } else {
          toast.error('Invoice ID not found in response.');
        }
      } else {
        toast.error(response.message);
      }
    }
  }

  const handlePaymentMethodChange = (method: string) => {
    setSelectedPaymentMethod(method);
  };

  return (
    <div className='max-md:px-3 flex md:mt-16 max-md:mt-5 md:justify-between max-md:flex-wrap max-md:gap-5'>
      <div className='md:w-[70%]'>
        <PaymentMethodCard 
          onPaymentMethodChange={handlePaymentMethodChange}
        />
        
            {orderSummaryData && (
                <OrderSummary {...orderSummaryData} />
            )}
      </div>
      <FactorCard
        steps={{title: "payment", nextStepBtnTitle: "پرداخت", nextStepBtnLink: "/checkout/result"}}
        paymentMethod={selectedPaymentMethod}
        onCreateInvoice={handleCreateInvoice}
         />
    </div>
  );
};

export default PaymentPageClient;
