import {GenericResponse, ProductFilterOptions, ProductResponse, ProductResponseData} from "@/lib/types/product.types";
import {toQueryParams} from "@/utils/helpers";
import envConfig from "@/lib/config-env";

/**
 * Fetches product data from the API based on the provided slug
 * @param slug - The product slug to fetch
 * @returns Promise with the product data response
 */
export async function getProduct(slug: string): Promise<ProductResponse> {
    console.log("___________________",slug);
    const res = await fetch(`http://127.0.0.1:8000/api/v1/products/${slug}`, {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        },
    });
    

    if (!res.ok) {
        throw new Error(`Failed to fetch product: ${res.status} ${res.statusText}`);
    }

    const data: ProductResponse = await res.json();
    return data;
}

export async function getProducts(productFilter: ProductFilterOptions): Promise<GenericResponse<ProductResponseData>> {
    const {has_guarantee_only, in_stock_only, page = 1, search, sort, min_price, max_price, limit = 9} = productFilter;
    const url = toQueryParams({
        page,
        limit,
        search,
        sort,
        min_price,
        max_price,
        in_stock_only,
        has_guarantee_only,
    });
    const env = envConfig();
    const baseUrl = env.BASE_URL_2
    console.log(env, '<<<<<<<<<<<<<<<<<<<<<<');
    const res = await fetch(`${baseUrl}products${url ? '?' + url : ''}`, {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
        },
    });

    if (!res.ok) {
        throw new Error(`Failed to fetch product: ${res.status} ${res.statusText}`);
    }

    const data = await res.json();
    return data;
}

export async function getProductsByCategory(categorySlug: string, productFilter: ProductFilterOptions): Promise<GenericResponse<ProductResponseData>> {
    const url = toQueryParams(productFilter);
    const env = envConfig();
    const baseUrl = env.BASE_URL_2
    const res = await fetch(`${baseUrl}categories/${categorySlug}/products${url ? '?' + url : ''}`, {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
        },
    });

    if (!res.ok) {
        throw new Error(`Failed to fetch productsByCategory: ${res.status} ${res.statusText}`);
    }

    const data = await res.json();
    return data;
}

/**
 * Fetches product help guides from the API
 * @param slug - The product slug to fetch guides for
 * @returns Promise with the product help guides response
 */
export async function getProductHelp(slug: string) {
    const res = await fetch(`http://127.0.0.1:8000/api/v1/products/${slug}/guides`, {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
        },
    });

    if (!res.ok) {
        throw new Error(`Failed to fetch product guides: ${res.status} ${res.statusText}`);
    }

    return res.json();
}

/**
 * Fetches product comments from the API
 * @param slug - The product slug to fetch comments for
 * @returns Promise with the product comments response
 */
export async function getProductComments(slug: string) {
    const res = await fetch(`http://127.0.0.1:8000/api/v1/products/${slug}/comments`, {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
        },
    });

    if (!res.ok) {
        throw new Error(`Failed to fetch product comments: ${res.status} ${res.statusText}`);
    }

    return res.json();
}

/**
 * Fetches product questions from the API
 * @param slug - The product slug to fetch questions for
 * @returns Promise with the product questions response
 */
export async function getProductQuestions(slug: string) {
    const res = await fetch(`http://127.0.0.1:8000/api/v1/products/${slug}/questions`, {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
        },
    });

    if (!res.ok) {
        throw new Error(`Failed to fetch product questions: ${res.status} ${res.statusText}`);
    }

    // Return the response with the correct type
    return res.json().then(data => {
        // Ensure the data conforms to the expected Question[] structure
        return {
            ...data,
            data: data.data.map((question: any) => ({
                ...question,
                answers: question.answers || [] // Ensure answers array exists
            }))
        };
    });
}

/**
 * Fetches products in shopping cart from the API
 * @returns Promise with the products response
 */
export async function getShoppingCart() {
    const res = await fetch(`http://127.0.0.1:8000/api/v1/cart`, {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': 'matin'
        },
    });

    if (!res.ok) {
        throw new Error(`Failed to fetch product comments: ${res.status} ${res.statusText}`);
    }

    return res.json();
}
