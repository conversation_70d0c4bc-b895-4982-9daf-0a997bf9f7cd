import { CartApiItem } from "../context/cart-context";
import { CartItemAttribute } from "./product.types";

export interface InvoiceProductDetail {
  key: string;
  value: string;
}

export interface InvoiceProduct {
  id: string;
  variant_id: string;
  name: string;
  price: number;
  sale_price: number;
  discount: number;
  quantity: number;
  total: number;
  image: string | null;
  details: InvoiceProductDetail[];
}

export interface InvoiceAddress {
  receiver_name: string;
  receiver_phone: string;
  address: string;
  province: string;
  city: string;
  zip_code: number;
  latitude: number;
  longitude: number;
}

export interface InvoiceData {
  id: string;
  status: string;
  creation_date: string;
  products: InvoiceProduct[];
  total: number;
  total_discount: number;
  address: InvoiceAddress;
}

// Main response type
export interface CreateInvoiceResponse {
  success: boolean;
  message: string;
  status: number;
  data: InvoiceData | {
    cart: string[];
  };
}



// get specefic invoice types 
export interface InvoiceResponse {
  success: boolean;
  message: string;
  status: number;
  data: SpecificInvoiceData;
}
export interface AllInvoicesResponse {
  success: boolean;
  message: string;
  status: number;
  data: SpecificInvoiceData[];
}

export interface SpecificInvoiceData {
  id: string;
  status: string;
  creation_date: string;
  products: CartApiItem[];
  subtotal: number;
  total_discount: number;
  total: number;
  address: InvoiceAddress;
  invoice_number: string
  transactions: Transaction[];
}

export interface Product {
  product_id: string;
  id: string;
  name: string;
  sku: string;
  price: number;
  sale_price: number | null;
  quantity: number;
  discount: number;
  total: number;
  image: string;
  in_stock: boolean;
  attributes: CartItemAttribute[];
}

export interface ProductAttribute {
  type: string;
  title: string;
  value: string;
  extra_data: {
    hex: string;
  } | null;
}

export interface InvoiceAddress {
  receiver_name: string;
  receiver_phone: string;
  address: string;
  province: string;
  city: string;
  zip_code: number;
  latitude: number;
  longitude: number;
}

export interface Transaction {
  user_id: string;
  status: string;
  amount: number;
  payment_method: string;
  payment_gateway: string;
  track_code: string | null;
  description: string;
  paid_at: string;
  created_at: string;
}
