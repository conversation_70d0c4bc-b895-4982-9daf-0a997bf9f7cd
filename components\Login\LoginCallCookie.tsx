"use client"
import { SetNaghlieCookie } from '@/actions/other.action'
import React, { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/lib/hooks/useAuth'

interface LoginCallCookieProps {
  token?: string
  tokenType?: string
  expireAt?: number
  paymentLink?: string
}
const LoginCallCookie: React.FC<LoginCallCookieProps> = ({ token, tokenType, expireAt, paymentLink }) => {
  const { reFetchUser } = useAuth()
  const router = useRouter()
  const [isReady, setIsReady] = useState(false)

  useEffect(() => {
      async function setUserCookie() {
      debugger
      try {
        if (token && tokenType) {
          // Format the token properly (Bearer + access_token)
          const fullToken = `${tokenType} ${token}`;
          await SetNaghlieCookie(fullToken, expireAt)

          // Refresh user data after setting cookie
          await reFetchUser()

          // Redirect to payment link if available
          if (paymentLink) {
            router.push("/")
            return; // Don't set isReady if redirecting
          }
        }
      } catch (error) {
        console.error('Error setting authorization cookie:', error);
      } finally {
        setIsReady(true)
      }
    }

    setUserCookie()
  }, [])

  if (!isReady) {
    return (
      <div className="flex justify-center items-center h-[90vh]">
      <div className="w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full animate-spin" />
    </div>
    )
  }

  return (
    <div className='hidden'></div>
  )
}

export default LoginCallCookie
