import {GenericResponse} from "@/lib/types/product.types";


import {Category, CategorySearchableAttributes} from "@/lib/types/category.types";
import envConfig from "@/lib/config-env";

class CategoryService {
    private baseUrl: string;

    constructor() {
        const env = envConfig();
        this.baseUrl = env.BASE_URL_2
    }

    private async request<T>(endpoint: string): Promise<GenericResponse<T>> {
        const res = await fetch(`${this.baseUrl}${endpoint}`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
            },
        });

        if (!res.ok) {
            throw new Error(`Failed to fetch ${endpoint}: ${res.status} ${res.statusText}`);
        }

        const data = await res.json();
        return data;
    }

    public getCategories(): Promise<GenericResponse<Category[]>> {
        return this.request<Category[]>('categories');
    }

    public getCategoryBreadCrumb(categorySlug: string): Promise<GenericResponse<Category[]>> {
        return this.request<Category[]>(`categories/${categorySlug}/breadcrumb`);
    }

    public getCategoriesBySlug(categorySlug: string): Promise<GenericResponse<Category[]>> {
        return this.request<Category[]>(`categories/${categorySlug}/tree`);
    }

    public getCategoryAttributes(categorySlug: string): Promise<GenericResponse<CategorySearchableAttributes[]>> {
        return this.request<CategorySearchableAttributes[]>(`categories/${categorySlug}/searchables`);
    }
}

export default new CategoryService();