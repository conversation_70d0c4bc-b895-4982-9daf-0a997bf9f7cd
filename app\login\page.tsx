import LoginBox from "@/components/Login/LoginBox";
import { apiClient } from "@/lib/apiClient";
import { Metadata } from "next";
export const metadata: Metadata = {
    robots: 'noindex, nofollow',
};

type Props = {
    searchParams?: Promise<{
        type?: string,
        withDetails?: string,
        plateLeft?: string,
        plateMiddle?: string,
        plateRight?: string,
        plateAlphabet?: string,
        phoneNumber?: string,
        nationalCode?: string,
        amount?: string
        userId?: string
        phone?: string
    }>
}
export default async function LoginPage({ searchParams }: Props) {
    const params = searchParams ? await searchParams : {};
    const {type, withDetails, plateLeft, plateMiddle, plateRight, plateAlphabet, phoneNumber, nationalCode, amount, userId, phone } = params;
    const redirectAuthResponse = await apiClient("auth/deposit/transfer", {
        method: "POST",
        body: {            
                userId,
                phone,
                type,
                withDetails,
                plateLeft,
                plateMiddle,
                plateRight,
                plateAlphabet,
                phoneNumber,
                nationalCode,
                amount,
                callback: `${baseurl}wallet/result`
        }
    })
    .then(res => res.json())
    console.log(redirectAuthResponse);
    // authorization
    return (
        <>
            <LoginBox />
        </>
    );
}
