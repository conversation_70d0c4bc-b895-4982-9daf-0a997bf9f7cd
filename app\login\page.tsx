import LoginBox from "@/components/Login/LoginBox";
import { apiClient } from "@/lib/apiClient";
import { Metadata } from "next";
import CookieService from "@/lib/services/cookie-service";
import { redirect } from "next/navigation";
import LoginCallCookie from "@/components/Login/LoginCallCookie";

export const metadata: Metadata = {
    robots: 'noindex, nofollow',
};

type Props = {
    searchParams?: Promise<{
        type?: string,
        withDetails?: string,
        plateLeft?: string,
        plateMiddle?: string,
        plateRight?: string,
        plateAlphabet?: string,
        phoneNumber?: string,
        nationalCode?: string,
        amount?: string
        userId?: string
        phone?: string
    }>
}
export default async function LoginPage({ searchParams }: Props) {
    const params = searchParams ? await searchParams : {};
    const {type, withDetails, plateLeft, plateMiddle, plateRight, plateAlphabet, phoneNumber, nationalCode, amount, userId, phone } = params;
    const redirectAuthResponse = await apiClient("auth/deposit/transfer", {
        method: "POST",
        body: {
                userId,
                phone,
                type,
                withDetails,
                plaque: {
                    plateLeft,
                    plateMiddle,
                    plateRight,
                    plateAlphabet
                },
                phoneNumber,
                nationalCode,
                amount,
                callback_url: `localhost:3000/wallet/result`
        }
    })
    .then(res => res.json())
    console.log(redirectAuthResponse);

    // // Check if authentication was successful and redirect to payment
    // if (redirectAuthResponse?.success && redirectAuthResponse?.data?.access_token && redirectAuthResponse?.data?.payment_link) {
    //     try {
    //         // Set authorization cookie with the access token
    //         const token = `${redirectAuthResponse.data.token_type} ${redirectAuthResponse.data.access_token}`;
    //         await CookieService.setAuthorizationToken(token, redirectAuthResponse.data.expire_at);

    //         // Redirect to payment link
    //         redirect(redirectAuthResponse.data.payment_link);
    //     } catch (error) {
    //         console.error('Error setting authorization cookie or redirecting:', error);
    //         // If there's an error, continue to show the login form
    //     }
    // }

    return (
        <>
            {redirectAuthResponse?.success && redirectAuthResponse?.data?.access_token && (
                <LoginCallCookie
                    token={redirectAuthResponse.data.access_token}
                    tokenType={redirectAuthResponse.data.token_type}
                    expireAt={redirectAuthResponse.data.expire_at}
                    paymentLink={redirectAuthResponse.data.payment_link}
                />
            )}
            <LoginBox />
        </>
    );
}
