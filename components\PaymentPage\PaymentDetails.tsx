import { Bill } from "@/lib/types/qabzino-types";
import PaymentDetailRow from "./PaymentDetailRow";

/**
 * Component for the payment details section
 * Displays all bill information in a structured format
 */
const PaymentDetails: React.FC<{ bill: Bill; totalAmount: number }> = ({ bill, totalAmount }) => (
  <div className="payment-card-body flex flex-col gap-3 mt-5">
    <PaymentDetailRow
      label="مبلغ"
      value={`${totalAmount.toLocaleString()} ریال`}
      className="mt-5 font-bold"
    />
    <PaymentDetailRow label="نوع قبض" value={bill.BillTypeShowName} />
    <PaymentDetailRow label="شناسه قبض" value={bill.BillID} />
    <PaymentDetailRow label="شناسه پرداخت" value={bill.PaymentID} />
    <PaymentDetailRow label="شماره ردیف" value={bill.RecordNumber} />
    {bill.TraceNumber && <PaymentDetailRow label="کد پیگیری" value={bill.TraceNumber} />}
    <PaymentDetailRow label="تاریخ" value={bill.TransactionDateTime} />
    <PaymentDetailRow label="درگاه" value="سداد" />
    {/* <PaymentDetailRow label="وضعیت" value={bill.Description} /> */}
    <div className="text-justify leading-6 max-md:text-sm">
      {bill.Description}
    </div>
  </div>
);

export default PaymentDetails