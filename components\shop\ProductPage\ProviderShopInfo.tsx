"use client"

import { Store, Award, ContainerIcon } from 'lucide-react'
import ProductSelectorCounter from './ProductSelectorCounter'
import { AttributeValue, Guarantee, Shop, Variation } from '@/lib/types/product.types'

interface ProviderShopInfoProps {
  shop?: Shop;
  variations?: Variation[];
  selectedColor?: AttributeValue | null;
  selectedSize?: string | null;
  selectedVariant?: Variation | null;
  productInfo?: {
    image?: string;
    name?: string;
  };
  guarantees?: Guarantee[];
}

const ProviderShopInfo = ({ shop, variations, selectedVariant, productInfo, guarantees }: ProviderShopInfoProps) => {
   console.log(shop);
   

  return (
    <div className='bg-[#F5F6F8] h-auto md:p-6 max-md:p-3 rounded-3xl'>
                <div className='flex justify-between border-b-2 pb-4 max-md:px-2 max-md:py-3'>
                    <button>
                        فروشنده
                    </button>
                    <button>
                        1 فروشنده دیگه
                    </button>
                </div>
                <div className='mt-5 pb-2 border-b-2 border-dashed'>
                    <span className='flex mb-3 gap-2 items-center text-gray-500'>
                        <Store />   {shop?.title || 'سپهر پلاس'}
                    </span>
                    <div className='flex text-[#9DA5B0] gap-2 mb-4'>
                        <div className=''>
                            <span className='text-yellow'>67%</span> <span>رضایت از کالا</span>
                        </div>
                        <span> | </span>
                        <p>عملکرد
                            <span className='text-[#2BA757]'> عالی</span>
                        </p>
                    </div>
                    {/* {(selectedColor || selectedSize) && (
                        <div className='bg-white p-2 rounded-lg mt-2 mb-1'>
                            {selectedColor && (
                                <p className='text-sm mb-1'>رنگ انتخاب شده: <span className='font-semibold'>{selectedColor.value}</span></p>
                            )}
                            {selectedSize && (
                                <p className='text-sm'>سایز انتخاب شده: <span className='font-semibold'>{selectedSize}</span></p>
                            )}
                        </div>
                    )} */}
                </div>
                <div>
                    <div className="relative inline-block text-gray-400 text-xl font-bold rtl mt-5">
                        <span>
                            {selectedVariant ?
                                `${selectedVariant.price.toLocaleString()} تومان` :
                                'محصول موجود نیست'
                            }
                        </span>

                        {/* Diagonal Line */}
                        {selectedVariant?.sale_price && (
                            <div className="absolute left-0 top-1/2 w-full border-t border-gray-500 rotate-[-10deg]"></div>
                        )}
                        {/* <div className="absolute left-0 top-1/2 w-full border-t border-gray-500 rotate-[-10deg]"></div> */}
                    </div>
                    {
                         selectedVariant?.sale_price &&
                        <>
                            <strong className='text-[#F81717] mr-3 md:hidden'>
                                    {(selectedVariant.sale_price)?.toLocaleString()}
                            </strong> <span className='md-hidden'>تومان</span>
                        </>
                    }
                    <div className='mt-3 mb-4'>
                        {
                            selectedVariant?.sale_price &&
                            <>
                                <strong className='text-[#F81717] max-md:hidden'>
                                    {(selectedVariant.sale_price)?.toLocaleString()}
                                </strong> <span className='max-md:hidden'>تومان</span>
                            </>
                        }
                        <p className='mt-2'>
                            تنها {selectedVariant ?
                                selectedVariant.current_quantity :
                                (variations?.length || 2)
                            } عدد در انبار باقی مانده
                        </p>
                    </div>

                   <ProductSelectorCounter selectedVariant={selectedVariant} productInfo={productInfo} />

                </div>
                <div className='mt-5 max-md:flex max-md:justify-between max-md:text-xs max-md:ga-1'>
                    <div className='bg-white rounded-3xl my-3 p-3 flex items-center gap-2'>
                        <Award />  {guarantees?.[0]?.company_name} ({guarantees?.[0]?.months} ماهه)
                    </div>
                    <div className='bg-white rounded-3xl my-3 p-3 flex items-center gap-2'>
                    <ContainerIcon />   ارسال به همه شهر ها
                    </div>
                </div>
            </div>
  )
}

export default ProviderShopInfo