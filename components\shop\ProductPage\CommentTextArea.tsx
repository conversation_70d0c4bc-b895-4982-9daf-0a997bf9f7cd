"use client"
import { useState } from 'react';

import { Star } from 'lucide-react';
import { createArticleComment, createProductComment } from '@/actions/comment.action';
// import SendAsDropdown from '@/components/common/SendAsDropdown';
import { useRouter } from 'next/navigation';
import toast from 'react-hot-toast';
type CommentTextAreaProps = {
    commentType: "product" | "blog"
    contentId: string
}

const CommentTextArea = ({commentType, contentId}: CommentTextAreaProps) => {
    const [rating, setRating] = useState(0);
    const [hover, setHover] = useState<null | number>(null);
    const [comment, setComment] = useState('');
    const [loading, setLoading] = useState(false)
    // const [media, setMedia] = useState<File | null>(null);
    const router = useRouter()
    console.log(router);
    

    const handleRating = (value: number) => {
        setRating(value);
    };

    //TODO: comment-out this section when media uploads are implemented
    // const handleMediaChange = (e: ChangeEvent<HTMLInputElement>) => {
    //     if (e.target.files && e.target.files.length > 0) {
    //         setMedia(e.target.files[0]);
    //     }
    // };

    const handleSubmit = async () => {
       
        // const reviewData = {
        //     rating,
        //     comment,
        //     // media,
        // };
        // console.log('Review submitted:', reviewData);
        const reviewData = {
            body: comment,
            rate: rating,
            id: contentId,
            reply_to: ""
        }
        setLoading(true)
        if (rating === 0) {
            toast.error("امتیاز دادن به کالا الزامی است")
            setLoading(false)
            return
        }
        let response
        debugger
        if (commentType === "product") {
             response = await createProductComment(reviewData)            
        } else {
             response = await createArticleComment(reviewData)
        }
        console.log(response);
        if (response.success) {
            toast.success("نظر شما با موفقیت ثبت شد")
            setComment('')
            setRating(0)
            setLoading(false)
        } else {
            toast.error(response.message || "خطایی رخ داده")
            setLoading(false)
        }
        
    };

    return (
        <div className="flex max-md:flex-col rounded-2xl border  w-full gap-4 bg-white text-right mb-5">

           
            <div className="flex-1 flex flex-col gap-3 p-4 ">
                <textarea
                    className="w-full min-h-[140px] p-3  rounded-xl text-right placeholder:text-gray-500 focus:outline-none "
                    placeholder="نظر خود را وارد بنویسید..."
                    value={comment}
                    onChange={(e) => setComment(e.target.value)}
                />

                {/* TODO: make this section dynamic for media uploads in product page */}

                {/* <div className="flex items-center gap-3 text-sm mt-4">
                    <label className="flex items-center gap-2 cursor-pointer bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-2 rounded-xl">
                        <UploadIcon size={20} />
                        افزودن عکس یا ویدیو
                        <input
                            type="file"
                            accept="image/*,video/*"
                            className="hidden"
                            onChange={handleMediaChange}
                        />
                    </label>

                    <SendAsDropdown />

                </div> */}
            </div>



            <div className='bg-gray-50 md:p-4 md:py-10 max-md:py-4'>
                <div className="w-[260px] max-md:w-full px-4 flex md:flex-col max-md:justify-between items-center max-md:items-start gap-4 ">
                    <div className='md:mx-auto'>
                        <div className="text-sm max-md:text-xs text-gray-500 mb-3">
                            {commentType === "product" ? "به این کالا چه امتیازی می‌دهید؟" : "به این مقاله چه امتیازی می‌دهید؟"}
                        </div>
                        <div className="flex gap-3 mb-4 md:mx-auto md:w-[90%]">
                            {[1, 2, 3, 4, 5].map((star) => (
                                <Star
                                    key={star}
                                    size={22}
                                    className={`cursor-pointer transition-colors fill-current ${(hover ?? rating) >= star ? 'text-yellow fill-yellow' : 'text-gray-300 fill-gray-300'
                                        }`}
                                    onClick={() => handleRating(star)}
                                    onMouseEnter={() => setHover(star)}
                                    onMouseLeave={() => setHover(null)}
                                />
                            ))}
                        </div>

                        <div className="flex max-md:justify-between md:gap-7 md:w-[94%] w-[94%] mx-auto text-xs text-gray-500 mb-4">
                            {[1, 2, 3, 4, 5].map((num) => (
                                <span key={num}>{num}</span>
                            ))}
                        </div>
                    </div>
                    <div className='w-full text-center flex justify-center'>
                        <button
                            onClick={handleSubmit}
                            disabled={loading}
                            className="w-full flex justify-center gap-3 items-center rounded-xl text-base max-md:py-4 py-2 bg-blue-600 text-white hover:bg-blue-700 transition"
                        >
                            {loading && <span className="loader border-t-transparent border-4 border-white rounded-full w-4 h-4 animate-spin"></span> }
                            
                            ارسال نظر
                        </button>

                    </div>

                </div>
            </div>
        </div>
    );
};

export default CommentTextArea;
