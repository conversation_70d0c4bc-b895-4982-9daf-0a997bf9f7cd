'use client'

import { useState, useEffect } from 'react'
import { ChevronDown } from 'lucide-react'
import { Attribute, AttributeValue, Variation } from '@/lib/types/product.types'
import { cn } from '@/lib/utils'

interface SelectAttributeProps {
    attributes: Attribute[]
    variations: Variation[]
    selectedColor?: AttributeValue | null
    defaultValue?: string | null
    onChange?: (value: string | null) => void
    disabled?: boolean
    selectedVariant: Variation | null
    attributeType?: string
    attributeTitle?: string
    hasColorDependency?: boolean
}

const SelectAttribute = ({
    variations,
    selectedColor,
    defaultValue = null,
    onChange,
    selectedVariant,
    disabled = false,
    attributeType = 'size',
    attributeTitle = 'سایز',
    hasColorDependency = true
}: SelectAttributeProps) => {
    const [isOpen, setIsOpen] = useState(false)
    const [selectedAttribute, setSelectedAttribute] = useState<string | null>(defaultValue)
    const [availableOptions, setAvailableOptions] = useState<string[]>([])


    /**
     * Update available options when selected color changes or when no color dependency exists
     * This function dynamically extracts values based on the attribute type
     */
    useEffect(() => {
        // If there's no color dependency, or if color is selected
        if (!hasColorDependency || selectedColor) {
            let filteredVariations = variations;

            // Only filter by color if color dependency exists and color is selected
            if (hasColorDependency && selectedColor) {
                filteredVariations = variations.filter(
                    (variant: Variation) => variant.color.toLowerCase() === selectedColor.value.toLowerCase()
                );
            }

            // Extract unique values from filtered variations based on attribute type dynamically
            let options: string[] = [];

            if (attributeType && filteredVariations.length > 0) {
                // Use the attributeType as the key to extract values from variations dynamically
                options = [...new Set(filteredVariations.map((variant: Variation) => {
                    // Access the property dynamically using bracket notation
                    const value = (variant as unknown as Record<string, string | number>)[attributeType];
                    return value ? String(value) : '';
                }).filter((value: string) => value !== ''))];
            }

            setAvailableOptions(options);

            // If current selected attribute is not available, reset it
            if (selectedAttribute && !options.includes(selectedAttribute)) {
                setSelectedAttribute(null);
                if (onChange) onChange(null);
            }

            // If there's only one option available, auto-select it
            if (options.length === 1 && (!selectedAttribute || selectedAttribute !== options[0])) {
                setSelectedAttribute(options[0]);
                if (onChange) onChange(options[0]);
            }

            console.log(`Available ${attributeType} options:`, options);
        } else {
            // Clear options if color dependency exists but no color is selected
            setAvailableOptions([]);
            setSelectedAttribute(null);
            if (onChange) onChange(null);
        }

        // Auto-select from selectedVariant if available (dynamic attribute access)
        if (selectedVariant && attributeType) {
            const variantValue = (selectedVariant as unknown as Record<string, string | number>)[attributeType];
            if (variantValue && String(variantValue) !== selectedAttribute) {
                setSelectedAttribute(String(variantValue));
                if (onChange) onChange(String(variantValue));
                console.log(`Auto-selected ${attributeType}:`, String(variantValue));
            }
        }

    }, [selectedColor, selectedVariant, variations, onChange, attributeType, hasColorDependency, selectedAttribute])



    const handleAttributeSelect = (value: string) => {
        setSelectedAttribute(value)
        setIsOpen(false)
        if (onChange) {
            onChange(value)
        }
    }


    return (
        <div className="relative md:w-52 mb-5">
            <button
                type="button"
                className={cn(
                    "flex items-center justify-between w-full px-4 py-2 text-sm border rounded-lg",
                    isOpen ? "border-gray-500" : "border-gray-300",
                    disabled ? "bg-gray-100 text-gray-500 cursor-not-allowed" : "bg-white hover:border-gray-400",
                    hasColorDependency && !selectedColor && "opacity-70"
                )}
                onClick={() => !disabled && setIsOpen(!isOpen)}
                disabled={disabled || (hasColorDependency && !selectedColor)}
            >
                <span>
                    {selectedAttribute ? selectedAttribute : `انتخاب ${attributeTitle}`}
                </span>
                <ChevronDown className={cn(
                    "w-4 h-4 transition-transform",
                    isOpen && "transform rotate-180"
                )} />
            </button>

            {isOpen && (
                <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg">
                    {availableOptions.length > 0 ? (
                        <ul className="py-1">
                            {availableOptions.map(option => (
                                <li
                                    key={option}
                                    className={cn(
                                        "px-4 py-2 text-sm cursor-pointer hover:bg-gray-100",
                                        selectedAttribute === option && "bg-gray-100 font-medium"
                                    )}
                                    onClick={() => handleAttributeSelect(option)}
                                >
                                    {option}
                                </li>
                            ))}
                        </ul>
                    ) : (
                        <div className="py-2 px-4 text-sm text-gray-500">
                            هیچ گزینه‌ای موجود نیست
                        </div>
                    )}
                </div>
            )}

            {/* {selectedColor && availableOptions.length === 0 && (
                <p className="mt-1 text-xs text-red-500">
                    هیچ {attributeTitle}ی برای این رنگ موجود نیست
                </p>
            )} */}
        </div>
    )
}

export default SelectAttribute
