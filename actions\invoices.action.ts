export async function getAllinvoices() {
    try {
        const response = await fetch(`http://127.0.0.1:8000/api/v1/invoices`, {
            method: "GET",
            headers: {
                "Authorization": "matin",
                "Content-Type": "application/json",
                "Accept": "application/json"
            }
        });
        const data = await response.json();
        return data
    } catch (error) {
        console.error("Error getting addresses:", error);
        return { success: false, error: "Failed to delete address" };
    }
}