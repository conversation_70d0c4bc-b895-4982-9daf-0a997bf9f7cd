"use client";

import { SpecificInvoiceData } from "@/lib/types/invoice.types";
import { CircleCheckBig, Eye, Ticket, XCircle } from "lucide-react";
import Link from "next/link";
import MoneyBagIcon from "../common/svg/MoneyBagIcon";

const orders = [
  { id: "01", orderNumber: "#254651", date: "30 آذر 1403", price: "480,000 تومان", status: "پرداخت شده", statusType: "delivered" },
  { id: "02", orderNumber: "#254652", date: "30 آذر 1403", price: "480,000 تومان", status: "منتظر پرداخت", statusType: "pending" },
  { id: "03", orderNumber: "#254653", date: "30 آذر 1403", price: "480,000 تومان", status: "در حال آماده سازی", statusType: "preparing" },
  { id: "04", orderNumber: "#254654", date: "30 آذر 1403", price: "480,000 تومان", status: "لغو شده", statusType: "canceled" },
  { id: "05", orderNumber: "#254655", date: "30 آذر 1403", price: "480,000 تومان", status: "پرداخت شده", statusType: "delivered" },
];
const statusMap = {
  paid: {
    label: "پرداخت شده",
    icon: <CircleCheckBig className="text-white" />,
    color: "text-[#6FEC94]",
    bg: "bg-[#6FEC94]/20 border-green-100",
  },
  pending: {
    label: "در انتظار پرداخت",
    icon: <MoneyBagIcon className="text-yellow-500" />,
    color: "text-yellow-500",
    bg: "bg-yellow-100 border-yellow-200",
  },
  reject: {
    label: "رد شده",
    icon: <XCircle className="text-red-500" />,
    color: "text-red-500",
    bg: "bg-red-100 border-red-200",
  },
} as const;

const LastOrders = ({ invoices }: { invoices: SpecificInvoiceData[] }) => {
  return (
    <div className="w-full h-auto mt-10 bg-white shadow-md rounded-3xl overflow-hidden px-2 max-md:order-5 py-5" dir="rtl">
      {/* Header */}
      <div className="flex justify-between items-center p-4">
        <div className="flex items-center gap-2">
          <Ticket />
          <h2 className="text-lg font-bold m-0">آخرین سفارشات</h2>
        </div>
        <Link href="#" className="text-gray-500 text-sm">
          مشاهده همه
        </Link>
      </div>

      {/* Responsive Table Container */}
      <div className="w-full mx-auto overflow-x-auto px-2 rounded-3xl p-1">
        <div className="hidden md:block">
          {/* Header */}
          <div className="grid grid-cols-10 bg-[#9DA5B0] text-white rounded-xl mb-3">
            <div className="py-3 px-4 text-right font-medium col-span-1">ردیف</div>
            <div className="py-3 px-4 text-right font-medium col-span-1">شناسه#</div>
            <div className="py-3 px-4 text-right font-medium col-span-1">تعداد</div>
            <div className="py-3 px-4 text-right font-medium col-span-2">تاریخ تراکنش</div>
            <div className="py-3 px-4 text-right font-medium col-span-2">مبلغ (تومان)</div>
            <div className="py-3 px-4 text-right font-medium col-span-3">وضعیت پرداخت</div>
          </div>

          {/* Rows */}
          {invoices.map((invoice, index) => {
            const status = invoice.status as keyof typeof statusMap;
            const current = statusMap[status] ?? statusMap.pending;
            return (
              <div
                key={invoice.id}
                className="grid grid-cols-10 bg-white odd:bg-gray-100 hover:bg-gray-50 transition rounded-xl overflow-hidden binvoice mb-2"
              >
                <div className="py-4 px-4 text-right col-span-1">{index + 1}</div>
                <div className="py-4 px-4 text-right col-span-1">{invoice.invoice_number}</div>
                <div className="py-4 px-4 text-right col-span-1">8</div>
                <div className="py-4 px-4 text-right col-span-2">{invoice.creation_date}</div>
                <div className="py-4 px-4 text-right col-span-2">{invoice.total}</div>
                <div className="py-4 px-4 text-right col-span-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <div className="w-9 h-9 bg-[#6FEC94] border-4 border-green-100 text-white flex justify-center items-center rounded-full">
                        <CircleCheckBig />
                      </div>
                      <span className="text-[#6FEC94]">{invoice.status}</span>
                    </div>
                    <button className="text-gray-400 hover:text-gray-600">
                      <Eye className="w-5 h-5" />
                    </button>
                  </div>
                </div>
              </div>
            )
          })}
        </div>



        {/* Mobile View - Card Layout */}
        <div className="md:hidden flex flex-col gap-3">
          {orders.map((order) => (
            <div key={order.id} className="bg-gray-100 p-4 rounded-lg shadow-sm flex flex-col gap-2">
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">شناسه سفارش: {order.orderNumber}</span>
                <button className="text-gray-400 hover:text-gray-600">
                  <Eye className="w-5 h-5" />
                </button>
              </div>
              <div className="text-gray-700 text-sm">{order.date}</div>
              <div className="text-gray-900 font-semibold">{order.price}</div>
              <div className="flex items-center gap-2">
                <div className="w-8 h-8 bg-[#6FEC94] border-2 border-green-100 text-white flex justify-center items-center rounded-full">
                  <CircleCheckBig className="w-4 h-4" />
                </div>
                <span className="text-[#6FEC94] text-sm">{order.status}</span>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default LastOrders;
